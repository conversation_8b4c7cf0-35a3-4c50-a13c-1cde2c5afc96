// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/flight/booking_summary.dart';
import 'package:culture_connect/providers/travel/flight_providers.dart';
import 'package:culture_connect/screens/payment/payment_screen.dart';
import 'package:culture_connect/widgets/common/error_view.dart';

/// Screen for confirming flight booking details and processing payment
class FlightBookingConfirmationScreen extends ConsumerStatefulWidget {
  /// Creates a new flight booking confirmation screen
  const FlightBookingConfirmationScreen({super.key});

  @override
  ConsumerState<FlightBookingConfirmationScreen> createState() =>
      _FlightBookingConfirmationScreenState();
}

class _FlightBookingConfirmationScreenState
    extends ConsumerState<FlightBookingConfirmationScreen> {
  // State variables
  bool _isLoading = false;
  bool _termsAccepted = false;
  bool _privacyAccepted = false;
  String? _errorMessage;
  BookingSummary? _bookingSummary;

  @override
  void initState() {
    super.initState();
    _generateBookingSummary();
  }

  void _generateBookingSummary() {
    try {
      setState(() => _isLoading = true);
      
      // Get data from providers
      final selectedFlight = ref.read(selectedFlightProvider);
      final passengerInfo = ref.read(passengerInfoProvider);
      final selectedSeats = ref.read(selectedSeatsProvider);
      final selectedServices = ref.read(selectedServicesProvider);
      
      if (selectedFlight == null || passengerInfo.isEmpty) {
        throw Exception('Missing flight or passenger information');
      }
      
      // Calculate fare breakdown (mock implementation)
      final basePrice = selectedFlight.price * passengerInfo.length;
      final seatFees = _calculateSeatFees(selectedSeats);
      final serviceFees = selectedServices.fold(0.0, (sum, service) => sum + service.price);
      final taxes = basePrice * 0.15; // 15% tax
      final fees = 25.0; // Fixed booking fee
      
      final fareBreakdown = FareBreakdown(
        baseFare: basePrice,
        taxes: taxes,
        fees: fees,
        discount: 0.0,
        totalPrice: basePrice + taxes + fees + seatFees + serviceFees,
        currency: selectedFlight.currency,
      );
      
      _bookingSummary = BookingSummary(
        flight: selectedFlight,
        passengers: passengerInfo,
        selectedSeats: selectedSeats,
        selectedServices: selectedServices,
        fareBreakdown: fareBreakdown,
        totalPrice: fareBreakdown.totalPrice,
      );
      
      setState(() => _isLoading = false);
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  double _calculateSeatFees(Map<String, String> selectedSeats) {
    // Mock seat fee calculation
    return selectedSeats.length * 20.0; // $20 per seat
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Booking Confirmation'),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
                  child: ErrorView(
                    error: _errorMessage!,
                    onRetry: _generateBookingSummary,
                  ),
                )
              : _bookingSummary == null
                  ? const Center(child: Text('No booking information available'))
                  : _buildConfirmationContent(theme),
      bottomNavigationBar: _buildPaymentButton(theme),
    );
  }

  Widget _buildConfirmationContent(ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFlightSummary(theme),
          const SizedBox(height: 24),
          _buildPassengerSummary(theme),
          const SizedBox(height: 24),
          _buildSeatSummary(theme),
          const SizedBox(height: 24),
          _buildFareBreakdown(theme),
          const SizedBox(height: 24),
          _buildTermsAndConditions(theme),
          const SizedBox(height: 100), // Space for bottom button
        ],
      ),
    );
  }

  Widget _buildFlightSummary(ThemeData theme) {
    final flight = _bookingSummary!.flight;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Flight Details',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        flight.segments.first.originCode,
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        flight.segments.first.originCity,
                        style: theme.textTheme.bodyMedium,
                      ),
                      Text(
                        flight.segments.first.formattedDepartureTime,
                        style: theme.textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.flight_takeoff,
                  color: theme.colorScheme.primary,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        flight.segments.first.destinationCode,
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        flight.segments.first.destinationCity,
                        style: theme.textTheme.bodyMedium,
                      ),
                      Text(
                        flight.segments.first.formattedArrivalTime,
                        style: theme.textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Flight Number: ${flight.segments.first.flightNumber}'),
                Text('Duration: ${flight.segments.first.duration}'),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Aircraft: ${flight.segments.first.aircraftType}'),
                Text('Class: ${flight.cabinClass}'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPassengerSummary(ThemeData theme) {
    final passengers = _bookingSummary!.passengers;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Passengers (${passengers.length})',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...passengers.asMap().entries.map((entry) {
              final index = entry.key;
              final passenger = entry.value;
              
              return Padding(
                padding: EdgeInsets.only(bottom: index < passengers.length - 1 ? 12 : 0),
                child: Row(
                  children: [
                    Icon(
                      _getPassengerTypeIcon(passenger.type),
                      color: theme.colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            passenger.fullName,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Text(
                            '${_getPassengerTypeLabel(passenger.type)} • ${passenger.nationality}',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurface.withAlpha(153),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildSeatSummary(ThemeData theme) {
    final selectedSeats = _bookingSummary!.selectedSeats;
    
    if (selectedSeats.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Seat Selection',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'No seats selected - seats will be assigned at check-in',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withAlpha(153),
                ),
              ),
            ],
          ),
        ),
      );
    }
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Selected Seats',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...selectedSeats.entries.map((entry) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(entry.key),
                    Text(
                      'Seat ${entry.value}',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildFareBreakdown(ThemeData theme) {
    final fareBreakdown = _bookingSummary!.fareBreakdown;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Fare Breakdown',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildFareRow(theme, 'Base Fare', fareBreakdown.baseFare, _bookingSummary!.flight.currency),
            _buildFareRow(theme, 'Taxes & Fees', fareBreakdown.taxes + fareBreakdown.fees, _bookingSummary!.flight.currency),
            if (_bookingSummary!.selectedSeats.isNotEmpty)
              _buildFareRow(theme, 'Seat Selection', _calculateSeatFees(_bookingSummary!.selectedSeats), _bookingSummary!.flight.currency),
            if (_bookingSummary!.selectedServices.isNotEmpty)
              _buildFareRow(theme, 'Additional Services', _bookingSummary!.additionalServicesTotal, _bookingSummary!.flight.currency),
            const Divider(),
            _buildFareRow(
              theme,
              'Total',
              _bookingSummary!.totalPrice,
              _bookingSummary!.flight.currency,
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFareRow(ThemeData theme, String label, double amount, String currency, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            '$currency ${amount.toStringAsFixed(2)}',
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? theme.colorScheme.primary : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTermsAndConditions(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Terms & Conditions',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            CheckboxListTile(
              value: _termsAccepted,
              onChanged: (value) {
                setState(() => _termsAccepted = value ?? false);
              },
              title: const Text('I accept the Terms and Conditions'),
              subtitle: GestureDetector(
                onTap: _showTermsDialog,
                child: Text(
                  'View Terms and Conditions',
                  style: TextStyle(
                    color: theme.colorScheme.primary,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
              controlAffinity: ListTileControlAffinity.leading,
              contentPadding: EdgeInsets.zero,
            ),
            CheckboxListTile(
              value: _privacyAccepted,
              onChanged: (value) {
                setState(() => _privacyAccepted = value ?? false);
              },
              title: const Text('I accept the Privacy Policy'),
              subtitle: GestureDetector(
                onTap: _showPrivacyDialog,
                child: Text(
                  'View Privacy Policy',
                  style: TextStyle(
                    color: theme.colorScheme.primary,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
              controlAffinity: ListTileControlAffinity.leading,
              contentPadding: EdgeInsets.zero,
            ),
          ],
        ),
      ),
    );
  }
